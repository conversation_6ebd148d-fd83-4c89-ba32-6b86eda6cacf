'''
from skillNer.skill_extractor_class import SkillExtractor
from skillNer.general_params import SKILL_DB
import spacy
import re
from typing import List, Set

# Load spaCy and SkillNER
nlp = spacy.load("en_core_web_lg")
skill_extractor = SkillExtractor(nlp, SKILL_DB)

def clean_skill(skill: str) -> str:
    """Clean and normalize skill text."""
    return skill.lower().strip()

def extract_skills(text: str) -> Set[str]:
    """
    Extract skills from text using SkillNER and pattern matching.
    Returns a set of cleaned and normalized skill names.
    """
    if not text or not isinstance(text, str):
        return set()
    
    skills = set()
    
    # Extract skills using SkillNER
    try:
        annotations = skill_extractor.annotate(text)
        # Get full matches
        full_matches = {clean_skill(match['doc_node_value']) 
                       for match in annotations['results']['full_matches']}
        # Get n-gram matches with confidence > 0.7
        ngram_matches = {clean_skill(match['doc_node_value'])
                        for match in annotations['results']['ngram_scored']
                        if match.get('score', 0) > 0.7}
        skills.update(full_matches.union(ngram_matches))
    except Exception as e:
        print(f"SkillNER extraction error: {e}")
    
    # Also look for common skill patterns
    skill_patterns = [
        r'\b(?:proficient in|experience with|skilled in|expertise in|knowledge of|familiar with)[\s:]+([\w\s/&+.-]+)(?:\.|,|;|$)',
        r'\b(?:technologies?|skills?|tools?)[\s:]+([\w\s/&+.-]+)(?:\.|,|;|$)'
    ]
    
    for pattern in skill_patterns:
        for match in re.finditer(pattern, text, re.IGNORECASE):
            skills.update(clean_skill(s) for s in re.split(r'[,/&+]', match.group(1)) if s.strip())
    
    return skills

def get_missing_skills(jd_text: str, resume_text: str) -> List[str]:
    """
    Returns a list of skills present in the job description but missing from the resume.
    """
    if not jd_text or not resume_text:
        return []  # Return empty list when inputs are invalid
    
    jd_skills = extract_skills(jd_text)
    resume_skills = extract_skills(resume_text)
    
    # Find skills in JD that aren't in resume
    missing_skills = jd_skills - resume_skills
    
    # Sort by relevance
    return sorted(list(missing_skills))
'''

import re
import numpy as np
from typing import List, Set, Tuple, Optional
import spacy
from bert_embedder import BertEmbedder

# Load spaCy for basic NLP
nlp = spacy.load("en_core_web_lg")

# Initialize BERT embedder
embedder = BertEmbedder()

def clean_phrase(text: str) -> str:
    """Clean and normalize text."""
    return re.sub(r'[^\w\s]', ' ', text.lower()).strip()

def extract_phrases(text: str) -> Set[str]:
    """
    Extract potential skill phrases using spaCy's noun chunks and named entities.
    Returns a set of cleaned phrases.
    """
    if not text or not text.strip():
        return set()
    
    doc = nlp(text)
    phrases = set()
    
    # Add noun chunks (e.g., 'machine learning', 'data analysis')
    for chunk in doc.noun_chunks:
        if len(chunk.text.split()) <= 3:  # Limit phrase length
            phrase = clean_phrase(chunk.text)
            if phrase and len(phrase) > 2:  # Filter out very short phrases
                phrases.add(phrase)
    
    # Add named entities (e.g., 'Python', 'AWS')
    for ent in doc.ents:
        if ent.label_ in ['ORG', 'PRODUCT', 'TECH']:
            phrase = clean_phrase(ent.text)
            if phrase and len(phrase) > 1:
                phrases.add(phrase)
    
    return phrases

def get_missing_skills(jd_text: str, resume_text: str, threshold: float = 0.7) -> List[Tuple[str, float]]:
    """
    Find skills in job description that are missing from the resume using BERT embeddings.
    
    Args:
        jd_text: Job description text
        resume_text: Resume text
        threshold: Similarity threshold (0-1) to consider phrases as matching
        
    Returns:
        List of (missing_skill, similarity) tuples, sorted by relevance
    """
    if not jd_text or not resume_text:
        return []
    
    # Extract phrases from both texts
    jd_phrases = list(extract_phrases(jd_text))
    resume_phrases = list(extract_phrases(resume_text))

    # Find skills in JD that aren't in resume
    missing_skills = jd_phrases - resume_phrases
    
    # Sort by relevance
    return sorted(list(missing_skills))


