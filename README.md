# Resume Optimizer

Resume Optimizer is a full-stack project that analyzes resumes against job descriptions using an advanced ATS scoring algorithm with SkillNER for skill extraction.

## Features
- **Advanced ATS Scoring**:
  - TF-IDF keyword similarity with n-grams
  - Skill extraction and matching using SkillNER
  - Formatting checks (sections, bullet points, fonts, tables/images)
  - Content structure analysis
- **Skill Analysis**:
  - Extracts and categorizes skills from both resume and job description
  - Identifies missing skills from job description
  - Provides skill-based improvement suggestions
- **Structured Resume Parsing** using NLP
- **Frontend** built with vanilla JavaScript
- **Backend** powered by FastAPI

---

## How It Works
1. User uploads a resume (PDF/DOCX) and pastes the job description.
2. Backend extracts:
   - Resume full text
   - Structured section data (headings, bullets, font sizes, etc.)
3. **ATSCalculator** is instantiated with the job description.
4. Backend computes:
   - Content score
   - Formatting score
   - Final ATS score (0–100)
5. **Only `final_score` is returned** to the frontend for display.

---

## Installation & Running

### Prerequisites
- Python 3.8+
- Node.js 14+ (for frontend development)
- spaCy English language model

### 1. Backend Setup
```bash
# Create and activate virtual environment
python -m venv venv
.\venv\Scripts\activate  # Windows
source venv/bin/activate  # Linux/Mac

# Install dependencies
pip install -r requirements.txt

# Install spaCy model
python -m spacy download en_core_web_lg

# Run the backend server
uvicorn main:app --reload
```

### 2. Frontend Setup
```bash
cd frontend
# Install dependencies (if using npm)
npm install

# Or if using yarn
yarn install

# Start the development server
npm start
# or
yarn start
```

PI
POST /process

Request:

jd_text – Job description (string)

file – Resume file (PDF/DOCX)

Response:

{
  "final_score": 82
}

Version Notes


ATS scoring logic enhanced with weighted skill coverage and  BERT integration.

Debug-level details are logged in the backend console for internal analysis.