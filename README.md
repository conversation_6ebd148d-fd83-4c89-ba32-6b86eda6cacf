# Resume Optimizer

Resume Optimizer is a full-stack project that analyzes resumes against job descriptions using an advanced ATS scoring algorithm.

## Features
- **Advanced ATS Scoring**:
  - TF-IDF keyword similarity with n-grams
  - Weighted hard & soft skill matching
  - Formatting checks (sections, bullet points, fonts, tables/images)
  - Optional BERT embeddings for semantic similarity
- **Structured Resume Parsing** using NLP
- **Frontend** built in React
- **Backend** powered by FastAPI

---

## How It Works
1. User uploads a resume (PDF/DOCX) and pastes the job description.
2. Backend extracts:
   - Resume full text
   - Structured section data (headings, bullets, font sizes, etc.)
3. **ATSCalculator** is instantiated with the job description.
4. Backend computes:
   - Content score
   - Formatting score
   - Final ATS score (0–100)
5. **Only `final_score` is returned** to the frontend for display.

---

## Installation & Running

### 1. Backend
```bash
cd backend
pip install -r requirements.txt
uvicorn main:app --reload

2. Frontend
cd frontend
npm install
npm start

PI
POST /process

Request:

jd_text – Job description (string)

file – Resume file (PDF/DOCX)

Response:

{
  "final_score": 82
}

Version Notes


ATS scoring logic enhanced with weighted skill coverage and  BERT integration.

Debug-level details are logged in the backend console for internal analysis.