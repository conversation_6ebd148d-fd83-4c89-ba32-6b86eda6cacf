# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
*.pyc
*.pyd
*.pyo
*.egg
.eggs/
*.egg-info/
.pytest_cache/
.coverage
htmlcov/
.mypy_cache/
.python-version

# Virtual Environment
venv/
env/
ENV/
.venv/

# Build and Distribution
build/
dist/
*.egg-info/
*.egg

# IDE and Editor
.idea/
.vscode/
*.swp
*.swo
*~
.DS_Store
Thumbs.db

# Logs and databases
*.log
*.sqlite
*.db

# Environment variables
.env
.env.local
.env.*.local

# Frontend
node_modules/

# Local development
.local/

# Jupyter Notebook
.ipynb_checkpoints/