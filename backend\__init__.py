"""
Backend package for Resume Optimizer ATS system.
Initializes the SkillNER extractor and other shared resources.
"""

import spacy
from spacy.matcher import PhraseMatcher
from skillNer.skill_extractor_class import SkillExtractor
from skillNer.general_params import SKILL_DB
from typing import Optional

# Initialize spaCy model
try:
    nlp = spacy.load("en_core_web_lg")
except OSError:
    try:
        nlp = spacy.load("en_core_web_sm")
    except OSError:
        raise ImportError(
            "spaCy model not found. Please install it using:\n"
            "python -m spacy download en_core_web_lg\n"
            "or\n"
            "python -m spacy download en_core_web_sm"
        )

# Initialize PhraseMatcher and SkillNER extractor
phrase_matcher = PhraseMatcher(nlp.vocab)
skill_extractor = SkillExtractor(nlp, SKILL_DB, phrase_matcher)

def get_skill_extractor():
    """Get the shared SkillNER extractor instance."""
    return skill_extractor

__all__ = ['get_skill_extractor', 'skill_extractor', 'nlp', 'SKILL_DB']