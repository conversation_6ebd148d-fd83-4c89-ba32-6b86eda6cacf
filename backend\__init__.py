"""
Backend package for Resume Optimizer ATS system.
Initializes the SkillNER extractor and other shared resources.
"""

import spacy
from skillner import SkillExtractor
from typing import Optional

# Initialize spaCy model
try:
    nlp = spacy.load("en_core_web_lg")
except OSError:
    raise ImportError(
        "spaCy model 'en_core_web_lg' not found. Please install it using:\n"
        "python -m spacy download en_core_web_lg"
    )

# Initialize SkillNER extractor
skill_extractor = SkillExtractor(nlp)

def get_skill_extractor():
    """Get the shared SkillNER extractor instance."""
    return skill_extractor

__all__ = ['get_skill_extractor', 'skill_extractor', 'nlp']