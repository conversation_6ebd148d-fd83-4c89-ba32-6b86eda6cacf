# bert_embedder.py
"""
Light wrapper around Sentence-Transformers to:
- Load a compact model once and cache it
- Provide cosine similarity helpers
- Offer encode() that returns NumPy arrays

Model: 'sentence-transformers/all-MiniLM-L6-v2' (fast, good for semantic similarity)
"""

from __future__ import annotations
from typing import List, Optional
import threading

import numpy as np
from sentence_transformers import SentenceTransformer

# Thread-safe singleton loader
class _ModelCache:
    _instance: Optional["BertEmbedder"] = None
    _lock = threading.Lock()


class BertEmbedder:
    """
    Usage:
        embedder = BertEmbedder()
        vecs = embedder.encode(["hello world", "goodbye"])
        sim = embedder.cosine_sim(vecs[0], vecs[1])
        max_sim = embedder.max_pair_sim(vecs[0], vecs_matrix)
    """

    MODEL_NAME = "sentence-transformers/all-MiniLM-L6-v2"

    def __new__(cls):
        # Singleton pattern to avoid reloading the model repeatedly
        if _ModelCache._instance is None:
            with _ModelCache._lock:
                if _ModelCache._instance is None:
                    obj = super().__new__(cls)
                    obj._model = SentenceTransformer(cls.MODEL_NAME)
                    _ModelCache._instance = obj
        return _ModelCache._instance

    def encode(self, texts: List[str]) -> np.ndarray:
        """
        Encode a list of strings into a NumPy array of shape (n, d).
        """
        if not texts:
            return np.zeros((0, 384), dtype=np.float32)  # 384 for MiniLM-L6-v2
        emb = self._model.encode(texts, convert_to_numpy=True, show_progress_bar=False, normalize_embeddings=True)
        # normalize_embeddings=True -> cosine sim is just dot product
        return emb

    @staticmethod
    def cosine_sim(vec_a: np.ndarray, vec_b: np.ndarray) -> float:
        """
        Cosine similarity between two 1D vectors (expects normalized vectors).
        If not normalized, it will compute a safe cosine.
        """
        a = np.array(vec_a, dtype=np.float32).reshape(-1)
        b = np.array(vec_b, dtype=np.float32).reshape(-1)
        # If they are normalized, dot is cosine; otherwise do full cosine.
        denom = (np.linalg.norm(a) * np.linalg.norm(b))
        if denom == 0:
            return 0.0
        return float(np.dot(a, b) / denom)

    @staticmethod
    def max_pair_sim(query_vec: np.ndarray, matrix_vecs: np.ndarray) -> float:
        """
        Given a single vector and a matrix of vectors, return the maximum cosine similarity.
        Assumes inputs may or may not be normalized (safe cosine is used).
        """
        if matrix_vecs.size == 0:
            return 0.0
        # Compute cosine sim across rows
        q = query_vec.reshape(1, -1)
        # Normalize defensively
        def _norm(x):
            n = np.linalg.norm(x, axis=1, keepdims=True)
            n[n == 0] = 1.0
            return x / n

        Q = _norm(q.astype(np.float32))
        M = _norm(matrix_vecs.astype(np.float32))
        sims = (Q @ M.T).reshape(-1)
        return float(np.max(sims))
