<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>About - Resume Optimizer</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <main>
        <h1>About Resume Optimizer</h1>
        <p>
            Resume Optimizer is a web application that analyzes your resume against a provided job description
            and produces a final ATS (Applicant Tracking System) compatibility score.
        </p>

        <h2>How It Works</h2>
        <ul>
            <li>Upload your resume and paste the job description</li>
            <li>Our backend parses and extracts structured resume data</li>
            <li>Advanced ATS scoring engine calculates a <strong>final score</strong> using:
                <ul>
                    <li>Keyword relevance (TF-IDF with n-grams)</li>
                    <li>Weighted skill matching (hard & soft skills)</li>
                    <li>Formatting and ATS-friendly checks</li>
                    <li>Optional BERT embeddings for semantic similarity</li>
                </ul>
            </li>
            <li>Only the final score is shown on the frontend — detailed analysis is kept internal</li>
        </ul>

        <h2>Why This Matters</h2>
        <p>
            Most companies use Applicant Tracking Systems to filter resumes automatically.
            This tool helps you identify weaknesses in your resume and tailor it for maximum match with the job description.
        </p>

        <h2>Version Notes</h2>
        <p>
            Latest update: Advanced ATS scoring integrated, /process endpoint now returns only <code>final_score</code>.
        </p>
    </main>
</body>
</html>
